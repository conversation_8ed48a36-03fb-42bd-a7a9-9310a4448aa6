package my.com.cmg.rms.security;

import static my.com.cmg.db.central.tables.SecUser.SEC_USER;

import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import my.com.cmg.rms.dto.security.SecUserDTO;
import my.com.cmg.rms.utils.LogUtil;
import org.jooq.DSLContext;
import org.jooq.Record;
import org.springframework.stereotype.Repository;

@Repository
@Slf4j
@AllArgsConstructor
public class SecUserRepositoryJooq {

  private final DSLContext dsl;

  public SecUserDTO findByUserId(Long userId) {
    Record record =
        dsl.select(
                SEC_USER.USER_ID,
                SEC_USER.USERNAME,
                SEC_USER.EMAIL,
                SEC_USER.DIVISION_CODE,
                SEC_USER.ROLE_NAME)
            .from(SEC_USER)
            .where(SEC_USER.USER_ID.eq(userId))
            .fetchOne();

    log.info(
        LogUtil.QUERY,
        dsl.renderInlined(
            dsl.select(
                    SEC_USER.USER_ID,
                    SEC_USER.USERNAME,
                    SEC_USER.EMAIL,
                    SEC_USER.DIVISION_CODE,
                    SEC_USER.ROLE_NAME)
                .from(SEC_USER)
                .where(SEC_USER.USER_ID.eq(userId))));

    return record != null ? record.into(SecUserDTO.class) : null;
  }
}
