package my.com.cmg.rms.security;

import java.util.List;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.stereotype.Repository;
import org.springframework.web.reactive.function.client.WebClient;
import reactor.core.publisher.Mono;

@Repository
@Slf4j
@AllArgsConstructor
public class SecUserRepositoryJooq {

  private final WebClient centralServiceWebClient;

  public SecUserDTO findByUserId(Long userId) {
    log.info("Fetching user by ID: {}", userId);
    return centralServiceWebClient
        .get()
        .uri("/user/{id}", userId)
        .retrieve()
        .bodyToMono(SecUserDTO.class)
        .onErrorResume(
            e -> {
              log.error("Error fetching user from central service: {}", e.getMessage());
              return Mono.empty();
            })
        .block();
  }

  public SecUserDTO findByUsername(String username) {
    log.info("Fetching user by username: {}", username);
    return centralServiceWebClient
        .get()
        .uri("/user/username/{username}", username)
        .retrieve()
        .bodyToMono(SecUserDTO.class)
        .onErrorResume(
            e -> {
              log.error("Error fetching user by username from central service: {}", e.getMessage());
              return Mono.empty();
            })
        .block();
  }

  public List<String> getRightListByUserId(Long userId) {
    log.info("Fetching rights for user ID: {}", userId);
    return centralServiceWebClient
        .get()
        .uri("/user/{id}/rights", userId)
        .retrieve()
        .bodyToMono(new ParameterizedTypeReference<List<String>>() {})
        .onErrorResume(
            e -> {
              log.error("Error fetching user rights from central service: {}", e.getMessage());
              return Mono.just(List.of());
            })
        .block();
  }

  public List<String> getRoleListByUserId(Long userId) {
    log.info("Fetching roles for user ID: {}", userId);
    return centralServiceWebClient
        .get()
        .uri("/user/{id}/roles", userId)
        .retrieve()
        .bodyToMono(new ParameterizedTypeReference<List<String>>() {})
        .onErrorResume(
            e -> {
              log.error("Error fetching user roles from central service: {}", e.getMessage());
              return Mono.just(List.of());
            })
        .block();
  }

  public List<String> getRightListByUsername(String username) {
    log.info("Fetching rights for username: {}", username);
    return centralServiceWebClient
        .get()
        .uri("/user/username/{username}/rights", username)
        .retrieve()
        .bodyToMono(new ParameterizedTypeReference<List<String>>() {})
        .onErrorResume(
            e -> {
              log.error(
                  "Error fetching user rights by username from central service: {}",
                  e.getMessage());
              return Mono.just(List.of());
            })
        .block();
  }

  public List<String> getRoleListByUsername(String username) {
    log.info("Fetching roles for username: {}", username);
    return centralServiceWebClient
        .get()
        .uri("/user/username/{username}/roles", username)
        .retrieve()
        .bodyToMono(new ParameterizedTypeReference<List<String>>() {})
        .onErrorResume(
            e -> {
              log.error(
                  "Error fetching user roles by username from central service: {}", e.getMessage());
              return Mono.just(List.of());
            })
        .block();
  }
}
