package my.com.cmg.rms.security;

import java.util.List;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

@Service
@AllArgsConstructor
public class AuthorizationService implements IAuthorizationService {

  private final ISecUserService secUserService;

  @Override
  public List<String> getRightListByUsername(String username) {
    return secUserService.getRightListByUsername(username);
  }

  @Override
  public List<String> getRoleListByUsername(String username) {
    return secUserService.getRoleListByUsername(username);
  }

  @Override
  public List<String> getRightListByUserId(Long userId) {
    return secUserService.getRightListByUserId(userId);
  }

  @Override
  public List<String> getRoleListByUserId(Long userId) {
    return secUserService.getRoleListByUserId(userId);
  }

  @Override
  public boolean hasRight(String username, String rightCode) {
    List<String> rights = getRightListByUsername(username);
    return rights != null && rights.contains(rightCode);
  }

  @Override
  public boolean hasRole(String username, String roleCode) {
    List<String> roles = getRoleListByUsername(username);
    return roles != null && roles.contains(roleCode);
  }

  @Override
  public boolean hasRightByUserId(Long userId, String rightCode) {
    List<String> rights = getRightListByUserId(userId);
    return rights != null && rights.contains(rightCode);
  }

  @Override
  public boolean hasRoleByUserId(Long userId, String roleCode) {
    List<String> roles = getRoleListByUserId(userId);
    return roles != null && roles.contains(roleCode);
  }
}
