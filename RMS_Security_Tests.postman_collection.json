{"info": {"name": "RMS Security Tests", "description": "Comprehensive security testing for RMS system", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "variable": [{"key": "base_url", "value": "http://localhost:8080", "type": "string"}, {"key": "jwt_token", "value": "", "type": "string"}, {"key": "user_id", "value": "1", "type": "string"}], "item": [{"name": "Development Mode Tests", "item": [{"name": "Dev - Get Request Types (No Auth)", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/v1/rms/utils/request_type", "host": ["{{base_url}}"], "path": ["api", "v1", "rms", "utils", "request_type"]}}, "response": []}, {"name": "Dev - Get Request List (No Auth)", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/v1/rms/request/list?page=1&size=10", "host": ["{{base_url}}"], "path": ["api", "v1", "rms", "request", "list"], "query": [{"key": "page", "value": "1"}, {"key": "size", "value": "10"}]}}, "response": []}, {"name": "Dev - Create Request (No Auth)", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"requestHeaderDtl\": {\n    \"title\": \"Dev Mode Test Request\",\n    \"reference\": \"DEV-001\",\n    \"intention\": \"TESTING\",\n    \"reason\": \"Development mode security test\"\n  },\n  \"requestDetails\": []\n}"}, "url": {"raw": "{{base_url}}/api/v1/rms/request/saveRequest", "host": ["{{base_url}}"], "path": ["api", "v1", "rms", "request", "saveRequest"]}}, "response": []}]}, {"name": "Production Mode Tests", "item": [{"name": "Prod - <PERSON> (Should Fail)", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/v1/rms/request/list", "host": ["{{base_url}}"], "path": ["api", "v1", "rms", "request", "list"]}}, "response": []}, {"name": "Prod - Only JWT (Should Fail - Missing X-Ref-Id)", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{jwt_token}}"}], "url": {"raw": "{{base_url}}/api/v1/rms/request/list", "host": ["{{base_url}}"], "path": ["api", "v1", "rms", "request", "list"]}}, "response": []}, {"name": "Prod - Only X-Ref-Id (Should Fail - Missing JWT)", "request": {"method": "GET", "header": [{"key": "X-Ref-Id", "value": "{{user_id}}"}], "url": {"raw": "{{base_url}}/api/v1/rms/request/list", "host": ["{{base_url}}"], "path": ["api", "v1", "rms", "request", "list"]}}, "response": []}, {"name": "Prod - Invalid X-Ref-Id (Should Fail)", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{jwt_token}}"}, {"key": "X-Ref-Id", "value": "999999"}], "url": {"raw": "{{base_url}}/api/v1/rms/request/list", "host": ["{{base_url}}"], "path": ["api", "v1", "rms", "request", "list"]}}, "response": []}, {"name": "Prod - <PERSON><PERSON> (Should Success)", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{jwt_token}}"}, {"key": "X-Ref-Id", "value": "{{user_id}}"}], "url": {"raw": "{{base_url}}/api/v1/rms/request/list?page=1&size=10", "host": ["{{base_url}}"], "path": ["api", "v1", "rms", "request", "list"], "query": [{"key": "page", "value": "1"}, {"key": "size", "value": "10"}]}}, "response": []}, {"name": "Prod - Create Request (<PERSON><PERSON>)", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{jwt_token}}"}, {"key": "X-Ref-Id", "value": "{{user_id}}"}], "body": {"mode": "raw", "raw": "{\n  \"requestHeaderDtl\": {\n    \"title\": \"Prod Mode Test Request\",\n    \"reference\": \"PROD-001\",\n    \"intention\": \"TESTING\",\n    \"reason\": \"Production mode security test\"\n  },\n  \"requestDetails\": []\n}"}, "url": {"raw": "{{base_url}}/api/v1/rms/request/saveRequest", "host": ["{{base_url}}"], "path": ["api", "v1", "rms", "request", "saveRequest"]}}, "response": []}]}, {"name": "Utility Endpoints", "item": [{"name": "Get Request Types", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/v1/rms/utils/request_type", "host": ["{{base_url}}"], "path": ["api", "v1", "rms", "utils", "request_type"]}}}, {"name": "Get Categories", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/v1/rms/utils/category", "host": ["{{base_url}}"], "path": ["api", "v1", "rms", "utils", "category"]}}}, {"name": "Get Intentions", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/v1/rms/utils/intention", "host": ["{{base_url}}"], "path": ["api", "v1", "rms", "utils", "intention"]}}}]}]}