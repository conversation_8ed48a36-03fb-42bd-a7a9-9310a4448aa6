package my.com.cmg.rms.config;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.reactive.function.client.WebClient;

@Configuration
public class WebClientConfig {

  @Value("${rms.central.service.url:http://localhost:8081}")
  private String centralServiceUrl;

  @Bean
  public WebClient.Builder webClientBuilder() {
    return WebClient.builder();
  }

  @Bean
  public WebClient centralServiceWebClient(WebClient.Builder builder) {
    return builder.baseUrl(centralServiceUrl + "/api/v1/mers/secure").build();
  }
}
