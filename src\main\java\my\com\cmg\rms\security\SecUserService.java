package my.com.cmg.rms.security;

import java.util.List;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

@Service
@AllArgsConstructor
public class SecUserService implements ISecUserService {

  private final SecUserRepositoryJooq userRepositoryJooq;

  @Override
  public SecUserDTO getByUserId(Long userId) {
    return userRepositoryJooq.findByUserId(userId);
  }

  @Override
  public SecUserDTO getByUsername(String username) {
    return userRepositoryJooq.findByUsername(username);
  }

  @Override
  public List<String> getRightListByUserId(Long userId) {
    return userRepositoryJooq.getRightListByUserId(userId);
  }

  @Override
  public List<String> getRoleListByUserId(Long userId) {
    return userRepositoryJooq.getRoleListByUserId(userId);
  }

  @Override
  public List<String> getRightListByUsername(String username) {
    return userRepositoryJooq.getRightListByUsername(username);
  }

  @Override
  public List<String> getRoleListByUsername(String username) {
    return userRepositoryJooq.getRoleListByUsername(username);
  }
}
