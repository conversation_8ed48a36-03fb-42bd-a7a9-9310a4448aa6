#!/bin/bash

# RMS JWT Token Generator Script
# This script helps you get a JWT token from Keycloak for testing

echo "🔐 RMS JWT Token Generator"
echo "=========================="

# Configuration
KEYCLOAK_URL="http://*********:8090/realms/phis-cloud/protocol/openid-connect/token"
CLIENT_ID="your-client-id"  # Replace with your actual client ID
CLIENT_SECRET="your-client-secret"  # Replace with your actual client secret

echo "Keycloak URL: $KEYCLOAK_URL"
echo "Client ID: $CLIENT_ID"
echo ""

# Check if curl is available
if ! command -v curl &> /dev/null; then
    echo "❌ Error: curl is not installed. Please install curl first."
    exit 1
fi

# Check if jq is available (optional, for pretty JSON)
if command -v jq &> /dev/null; then
    HAS_JQ=true
else
    HAS_JQ=false
    echo "⚠️  Note: jq is not installed. Install it for better JSON formatting."
fi

echo "🚀 Getting JWT token..."
echo ""

# Make the request
RESPONSE=$(curl -s -X POST "$KEYCLOAK_URL" \
  -H "Content-Type: application/x-www-form-urlencoded" \
  -d "grant_type=client_credentials" \
  -d "client_id=$CLIENT_ID" \
  -d "client_secret=$CLIENT_SECRET")

# Check if request was successful
if [ $? -ne 0 ]; then
    echo "❌ Error: Failed to connect to Keycloak server"
    exit 1
fi

# Parse response
if [ "$HAS_JQ" = true ]; then
    # Pretty print with jq
    echo "📋 Full Response:"
    echo "$RESPONSE" | jq .
    echo ""
    
    # Extract access token
    ACCESS_TOKEN=$(echo "$RESPONSE" | jq -r '.access_token // empty')
    
    if [ -n "$ACCESS_TOKEN" ] && [ "$ACCESS_TOKEN" != "null" ]; then
        echo "✅ Success! JWT Token obtained:"
        echo "🔑 Access Token:"
        echo "$ACCESS_TOKEN"
        echo ""
        echo "📝 Copy this token to your Postman environment variable 'jwt_token'"
        echo ""
        echo "🧪 Test the token:"
        echo "curl -H \"Authorization: Bearer $ACCESS_TOKEN\" http://localhost:8080/api/v1/rms/request/list"
    else
        echo "❌ Error: No access token in response"
        echo "Response: $RESPONSE"
    fi
else
    # Without jq
    echo "📋 Response:"
    echo "$RESPONSE"
    echo ""
    echo "📝 Look for 'access_token' in the response above"
    echo "Copy the token value to your Postman environment variable 'jwt_token'"
fi

echo ""
echo "🔧 Configuration Notes:"
echo "1. Update CLIENT_ID and CLIENT_SECRET in this script"
echo "2. Ensure your Keycloak client has 'Service Accounts Enabled'"
echo "3. Grant appropriate roles to the service account"
echo ""
echo "📚 For more help, see: RMS_Security_Testing_Guide.md"
