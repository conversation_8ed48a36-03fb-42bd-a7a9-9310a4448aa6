# 🔐 RMS Security Testing Guide with Postman

## Overview
Your RMS system has two security modes:
1. **Development Mode** (`rms.development.mode=true`) - Permits all requests
2. **Production Mode** (`rms.development.mode=false`) - Requires JWT + X-Ref-Id header

## 🚀 Setup Instructions

### 1. Start Your Application
```bash
# Make sure your application is running
./gradlew bootRun
# or
mvn spring-boot:run
```

### 2. Base URL
```
http://localhost:8080
```

## 📋 Test Scenarios

### **Scenario 1: Development Mode Testing**

#### Configuration
Set in `application.properties`:
```properties
rms.development.mode=true
```

#### Test Cases

**✅ Test 1.1: Access Public Endpoints**
- **Method**: GET
- **URL**: `http://localhost:8080/api/v1/rms/utils/request_type`
- **Headers**: None required
- **Expected**: 200 OK with data

**✅ Test 1.2: Access Protected Endpoints**
- **Method**: GET  
- **URL**: `http://localhost:8080/api/v1/rms/request/list`
- **Headers**: None required
- **Expected**: 200 OK (should work without authentication)

**✅ Test 1.3: POST Request**
- **Method**: POST
- **URL**: `http://localhost:8080/api/v1/rms/request/saveRequest`
- **Headers**: `Content-Type: application/json`
- **Body**: 
```json
{
  "requestHeaderDtl": {
    "title": "Test Request",
    "reference": "REF001",
    "intention": "Testing",
    "reason": "Security Test"
  }
}
```
- **Expected**: 201 Created

---

### **Scenario 2: Production Mode Testing**

#### Configuration
Set in `application.properties`:
```properties
rms.development.mode=false
```

#### Test Cases

**❌ Test 2.1: Access Without Authentication**
- **Method**: GET
- **URL**: `http://localhost:8080/api/v1/rms/request/list`
- **Headers**: None
- **Expected**: 401 Unauthorized

**❌ Test 2.2: Access With Invalid X-Ref-Id**
- **Method**: GET
- **URL**: `http://localhost:8080/api/v1/rms/request/list`
- **Headers**: 
  - `X-Ref-Id: 999999`
- **Expected**: 401 Unauthorized - "Invalid User ID"

**❌ Test 2.3: Access Without X-Ref-Id Header**
- **Method**: GET
- **URL**: `http://localhost:8080/api/v1/rms/request/list`
- **Headers**: 
  - `Authorization: Bearer <valid-jwt-token>`
- **Expected**: 401 Unauthorized - "Missing X-Ref-Id header"

**✅ Test 2.4: Valid Authentication (JWT + X-Ref-Id)**
- **Method**: GET
- **URL**: `http://localhost:8080/api/v1/rms/request/list`
- **Headers**: 
  - `Authorization: Bearer <valid-jwt-token>`
  - `X-Ref-Id: <valid-user-id>`
- **Expected**: 200 OK with data

## 🔑 JWT Token Setup

### Option 1: Get JWT from Keycloak
```bash
# Get token from your Keycloak server
curl -X POST "http://*********:8090/realms/phis-cloud/protocol/openid-connect/token" \
  -H "Content-Type: application/x-www-form-urlencoded" \
  -d "grant_type=client_credentials" \
  -d "client_id=your-client-id" \
  -d "client_secret=your-client-secret"
```

### Option 2: Create Test JWT (for testing only)
Use [jwt.io](https://jwt.io) to create a test token with:
- **Header**: `{"alg": "RS256", "typ": "JWT"}`
- **Payload**: `{"sub": "test-user", "iss": "http://*********:8090/realms/phis-cloud"}`

## 📊 Postman Collection Structure

### Environment Variables
Create a Postman environment with:
```json
{
  "base_url": "http://localhost:8080",
  "jwt_token": "your-jwt-token-here",
  "user_id": "1",
  "keycloak_url": "http://*********:8090/realms/phis-cloud"
}
```

### Pre-request Scripts
Add this to collection pre-request script:
```javascript
// Auto-set Authorization header if jwt_token exists
if (pm.environment.get("jwt_token")) {
    pm.request.headers.add({
        key: "Authorization",
        value: "Bearer " + pm.environment.get("jwt_token")
    });
}
```

## 🧪 Test Endpoints

### Utils Endpoints (Usually Public)
- `GET /api/v1/rms/utils/request_type`
- `GET /api/v1/rms/utils/category`
- `GET /api/v1/rms/utils/intention`

### Request Endpoints (Protected)
- `GET /api/v1/rms/request/list`
- `GET /api/v1/rms/request/list/page`
- `POST /api/v1/rms/request/saveRequest`
- `GET /api/v1/rms/request/{id}`
- `PUT /api/v1/rms/request/updateRequest/{id}`
- `POST /api/v1/rms/request/confirmRequest/{id}`

## 🔍 Security Validation Checklist

### Development Mode ✅
- [ ] All endpoints accessible without authentication
- [ ] No JWT validation
- [ ] No X-Ref-Id header required

### Production Mode ✅
- [ ] Endpoints return 401 without JWT
- [ ] Endpoints return 401 without X-Ref-Id
- [ ] Endpoints return 401 with invalid X-Ref-Id
- [ ] Endpoints return 200 with valid JWT + X-Ref-Id
- [ ] JWT signature validation works
- [ ] User lookup via X-Ref-Id works

## 🚨 Common Issues & Solutions

### Issue 1: "Invalid User ID"
**Cause**: X-Ref-Id doesn't exist in database
**Solution**: Use a valid user ID from your `sec_user` table

### Issue 2: JWT Validation Failed
**Cause**: Invalid JWT or wrong issuer
**Solution**: Ensure JWT is from correct Keycloak realm

### Issue 3: CORS Issues
**Solution**: Add CORS configuration if testing from browser

## 📝 Sample Test Data

### Valid User IDs (check your database)
```sql
SELECT usr_id, usr_firstname, usr_lastname 
FROM phisprod.sec_user 
LIMIT 5;
```

### Sample Request Body
```json
{
  "requestHeaderDtl": {
    "title": "Security Test Request",
    "reference": "SEC-001",
    "intention": "TESTING",
    "reason": "Security validation"
  },
  "requestDetails": []
}
```
