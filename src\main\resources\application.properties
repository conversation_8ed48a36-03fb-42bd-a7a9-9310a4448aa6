spring.application.name=rms

spring.datasource.url=************************************
spring.jpa.properties.hibernate.default_schema=phisprod
spring.datasource.username=ihisblank
spring.datasource.password=ihisblank
spring.security.oauth2.resourceserver.jwt.issuer-uri=http://10.1.2.98:8090/realms/phis-cloud

# Security Configuration
rms.development.mode=false
rms.central.service.url=http://10.1.2.98:8081
