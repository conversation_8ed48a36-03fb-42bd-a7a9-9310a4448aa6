package my.com.cmg.rms.security;

import java.util.List;

public interface IAuthorizationService {
  List<String> getRightListByUsername(String username);

  List<String> getRoleListByUsername(String username);

  List<String> getRightListByUserId(Long userId);

  List<String> getRoleListByUserId(Long userId);

  boolean hasRight(String username, String rightCode);

  boolean hasRole(String username, String roleCode);

  boolean hasRightByUserId(Long userId, String rightCode);

  boolean hasRoleByUserId(Long userId, String roleCode);
}
