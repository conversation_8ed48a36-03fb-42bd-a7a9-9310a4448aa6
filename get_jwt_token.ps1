# RMS JWT Token Generator Script (PowerShell)
# This script helps you get a JWT token from Keycloak for testing

Write-Host "🔐 RMS JWT Token Generator" -ForegroundColor Cyan
Write-Host "==========================" -ForegroundColor Cyan

# Configuration
$KEYCLOAK_URL = "http://*********:8090/realms/phis-cloud/protocol/openid-connect/token"
$CLIENT_ID = "your-client-id"  # Replace with your actual client ID
$CLIENT_SECRET = "your-client-secret"  # Replace with your actual client secret

Write-Host "Keycloak URL: $KEYCLOAK_URL" -ForegroundColor Yellow
Write-Host "Client ID: $CLIENT_ID" -ForegroundColor Yellow
Write-Host ""

Write-Host "🚀 Getting JWT token..." -ForegroundColor Green
Write-Host ""

try {
    # Prepare the request body
    $body = @{
        grant_type = "client_credentials"
        client_id = $CLIENT_ID
        client_secret = $CLIENT_SECRET
    }

    # Make the request
    $response = Invoke-RestMethod -Uri $KEYCLOAK_URL -Method Post -Body $body -ContentType "application/x-www-form-urlencoded"

    if ($response.access_token) {
        Write-Host "✅ Success! JWT Token obtained:" -ForegroundColor Green
        Write-Host ""
        Write-Host "📋 Full Response:" -ForegroundColor Cyan
        $response | ConvertTo-Json -Depth 3
        Write-Host ""
        
        Write-Host "🔑 Access Token:" -ForegroundColor Yellow
        Write-Host $response.access_token -ForegroundColor White
        Write-Host ""
        
        Write-Host "📝 Copy this token to your Postman environment variable 'jwt_token'" -ForegroundColor Magenta
        Write-Host ""
        
        Write-Host "🧪 Test the token with curl:" -ForegroundColor Cyan
        Write-Host "curl -H `"Authorization: Bearer $($response.access_token)`" http://localhost:8080/api/v1/rms/request/list" -ForegroundColor Gray
        
        # Copy to clipboard if available
        try {
            $response.access_token | Set-Clipboard
            Write-Host ""
            Write-Host "📋 Token copied to clipboard!" -ForegroundColor Green
        } catch {
            Write-Host ""
            Write-Host "⚠️  Could not copy to clipboard. Please copy manually." -ForegroundColor Yellow
        }
    } else {
        Write-Host "❌ Error: No access token in response" -ForegroundColor Red
        Write-Host "Response: $($response | ConvertTo-Json)" -ForegroundColor Red
    }
} catch {
    Write-Host "❌ Error: Failed to get token from Keycloak" -ForegroundColor Red
    Write-Host "Error details: $($_.Exception.Message)" -ForegroundColor Red
    
    if ($_.Exception.Response) {
        $statusCode = $_.Exception.Response.StatusCode
        Write-Host "HTTP Status: $statusCode" -ForegroundColor Red
    }
}

Write-Host ""
Write-Host "🔧 Configuration Notes:" -ForegroundColor Cyan
Write-Host "1. Update CLIENT_ID and CLIENT_SECRET in this script" -ForegroundColor White
Write-Host "2. Ensure your Keycloak client has 'Service Accounts Enabled'" -ForegroundColor White
Write-Host "3. Grant appropriate roles to the service account" -ForegroundColor White
Write-Host ""
Write-Host "📚 For more help, see: RMS_Security_Testing_Guide.md" -ForegroundColor Magenta

# Pause to keep window open
Write-Host ""
Write-Host "Press any key to continue..." -ForegroundColor Gray
$null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
