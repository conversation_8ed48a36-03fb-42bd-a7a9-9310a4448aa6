package my.com.cmg.rms.security;

import static org.mockito.ArgumentMatchers.anyLong;
import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureTestMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.test.context.TestPropertySource;
import org.springframework.test.web.servlet.MockMvc;

@SpringBootTest
@AutoConfigureTestMvc
@TestPropertySource(properties = {"rms.development.mode=true"})
class SecurityConfigTest {

  @Autowired private MockMvc mockMvc;

  @MockBean private ISecUserService secUserService;

  @Test
  void testDevelopmentModePermitsAllRequests() throws Exception {
    // In development mode, all requests should be permitted without authentication
    mockMvc.perform(get("/api/v1/rms/utils/request_type")).andExpect(status().isOk());
  }

  @Test
  void testDevelopmentModePermitsProtectedEndpoints() throws Exception {
    // In development mode, even protected endpoints should be accessible
    mockMvc.perform(get("/api/v1/rms/request/list")).andExpect(status().isOk());
  }
}

@SpringBootTest
@AutoConfigureTestMvc
@TestPropertySource(properties = {"rms.development.mode=false"})
class SecurityConfigProductionTest {

  @Autowired private MockMvc mockMvc;

  @MockBean private ISecUserService secUserService;

  @Test
  void testProductionModeRequiresAuthentication() throws Exception {
    // In production mode, requests without authentication should be rejected
    mockMvc.perform(get("/api/v1/rms/request/list")).andExpect(status().isUnauthorized());
  }

  @Test
  void testProductionModeWithValidUserIdHeader() throws Exception {
    // Mock user service to return a valid user
    SecUserDTO mockUser = new SecUserDTO();
    mockUser.setUserId(1L);
    mockUser.setUsername("testuser");
    when(secUserService.getByUserId(anyLong())).thenReturn(mockUser);

    // Request with valid X-Ref-Id header should work
    mockMvc
        .perform(get("/api/v1/rms/request/list").header("X-Ref-Id", "1"))
        .andExpect(status().isOk());
  }

  @Test
  void testProductionModeWithInvalidUserIdHeader() throws Exception {
    // Mock user service to return null for invalid user
    when(secUserService.getByUserId(anyLong())).thenReturn(null);

    // Request with invalid X-Ref-Id header should be rejected
    mockMvc
        .perform(get("/api/v1/rms/request/list").header("X-Ref-Id", "999"))
        .andExpect(status().isUnauthorized());
  }
}
